import { createRouter, createWebHashHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/Home.vue'),
    meta: { requiresAuth: true },
    beforeEnter: (_, __, next) => {
      const userStr = localStorage.getItem('user')
      const user = userStr ? JSON.parse(userStr) : null

      if (user) {
        if (user.role === 'ROLE_ADMIN') {
          next({ path: '/admin/books' })
        } else {
          next({ path: '/user/books' })
        }
      } else {
        next({ path: '/login' })
      }
    }
  },
  // 管理员路由
  {
    path: '/admin',
    name: 'Admin',
    component: () => import('../views/admin/AdminLayout.vue'),
    // meta: { requiresAuth: true, requiresAdmin: true },
    children: [
      {
        path: 'books',
        name: 'AdminBooks',
        component: () => import('../views/Books.vue')
      },
      {
        path: 'users',
        name: 'AdminUsers',
        component: () => import('../views/Users.vue')
      },
      {
        path: 'borrowing',
        name: 'AdminBorrowing',
        component: () => import('../views/Borrowing.vue')
      },
      {
        path: 'statistics',
        name: 'AdminStatistics',
        component: () => import('../views/Statistics.vue')
      },
      {
        path: 'analytics',
        name: 'AdminAnalytics',
        component: () => import('../views/admin/DataAnalytics.vue')
      },
      {
        path: 'broadcast',
        name: 'AdminBroadcast',
        component: () => import('../views/admin/SystemBroadcast.vue')
      },
      {
        path: 'api-test',
        name: 'AdminApiTest',
        component: () => import('../views/ApiTest.vue')
      }
    ]
  },
  // 普通用户路由
  {
    path: '/user',
    name: 'UserHome',
    component: () => import('../views/user/UserLayout.vue'),
    // meta: { requiresAuth: true },
    children: [
      {
        path: 'books',
        name: 'UserBooks',
        component: () => import('../views/user/UserBooks.vue')
      },
      {
        path: 'borrowing',
        name: 'UserBorrowing',
        component: () => import('../views/user/UserBorrowing.vue')
      },
      {
        path: 'profile',
        name: 'UserProfile',
        component: () => import('../views/user/UserProfile.vue')
      },
      {
        path: 'chatbot',
        name: 'UserChatBot',
        component: () => import('../views/user/UserChatBot.vue')
      },
      {
        path: 'notifications',
        name: 'UserNotifications',
        component: () => import('../views/user/UserNotifications.vue')
      }
    ]
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue')
  },
  {
    path: '/github/callback',
    name: 'GithubCallback',
    component: () => import('../views/GithubCallback.vue')
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, _, next) => {
  const userStr = localStorage.getItem('user')
  const user = userStr ? JSON.parse(userStr) : null

  if (to.matched.some(record => record.meta.requiresAuth)) {
    // 需要登录的页面
    if (!user) {
      // 未登录，跳转到登录页
      next({ name: 'Login' })
    } else if (to.matched.some(record => record.meta.requiresAdmin) && user.role !== 'ROLE_ADMIN') {
      // 需要管理员权限但用户不是管理员
      next({ path: '/user/books' }) // 重定向到普通用户页面
    } else {
      // 已登录且权限符合，允许访问
      next()
    }
  } else {
    // 不需要登录的页面，直接访问
    next()
  }
})

export default router
