// API服务层 - 与后端API通信
import axios from 'axios';

// API基础URL
const API_BASE_URL = 'http://localhost:8080';

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 获取存储在localStorage中的认证令牌
const getAuthToken = () => {
  const user = JSON.parse(localStorage.getItem('user'));
  return user ? user.token : null;
};

// 请求拦截器 - 自动添加认证头
apiClient.interceptors.request.use(
  (config) => {
    // 确保headers对象存在
    if (!config.headers) {
      config.headers = {};
    }

    const token = getAuthToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
      console.log('添加Authorization头:', `Bearer ${token.substring(0, 20)}...`);
    } else {
      console.log('未找到token，跳过Authorization头');
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 统一处理响应和错误
apiClient.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    console.error('API请求错误:', error);

    // 处理401未授权错误
    if (error.response?.status === 401) {
      // 清除本地存储的用户信息
      localStorage.removeItem('user');
      // 可以在这里触发重新登录
      window.location.href = '/login';
    }

    // 返回错误信息
    const errorMessage = error.response?.data?.message || error.message || '网络错误';
    return Promise.reject(new Error(errorMessage));
  }
);

// 通用请求方法
const request = async (url, method = 'GET', data = null, customBaseURL = null) => {
  try {
    const config = {
      method,
      url,
    };

    // 如果指定了自定义 baseURL，则创建新的 axios 实例
    let client = apiClient;
    if (customBaseURL) {
      client = axios.create({
        baseURL: customBaseURL,
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      // 为自定义客户端添加请求拦截器
      client.interceptors.request.use(
        (config) => {
          if (!config.headers) {
            config.headers = {};
          }
          const token = getAuthToken();
          if (token) {
            config.headers.Authorization = `Bearer ${token}`;
          }
          return config;
        },
        (error) => Promise.reject(error)
      );
    }

    if (data) {
      if (method.toLowerCase() === 'get') {
        config.params = data;
      } else {
        config.data = data;
      }
    }

    const response = await client(config);
    const responseData = customBaseURL ? response.data : response;
    return {
      success: true,
      data: responseData,
      message: responseData.message
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      data: null
    };
  }
};

// 导出API方法
export default {
  // 系统信息接口
  system: {
    getInfo: () => request('/'),
    getHealth: () => request('/health')
  },

  // 认证相关
  auth: {
    login: (credentials) => request('/api/auth/login', 'POST', credentials),
    register: (userData) => request('/api/auth/register', 'POST', userData),
    logout: () => request('/api/auth/logout', 'POST'),

  },

  // 图书管理API
  books: {
    // 获取所有图书
    getAll: () => request('/api/books/'),
    // 根据ID获取图书
    getById: (id) => request(`/api/books/${id}`),
    // 搜索图书
    searchByTitle: (title) => request('/api/books/search/title', 'GET', { title }),
    searchByAuthor: (author) => request('/api/books/search/author', 'GET', { author }),
    searchByCategory: (category) => request('/api/books/search/category', 'GET', { category }),
    // 添加新书 (管理员权限)
    create: (bookData) => request('/api/books', 'POST', bookData),
    // 更新图书信息 (管理员权限)
    update: (id, bookData) => request(`/api/books/${id}`, 'PUT', bookData),
    // 删除图书 (管理员权限)
    delete: (id) => request(`/api/books/${id}`, 'DELETE')
  },

  // 用户个人借阅管理API
  userBooks: {
    // 获取我的借阅列表
    getMyBooks: () => request('/api/users/me/books'),
    // 我要借阅图书
    borrowBook: (bookId) => request(`/api/users/me/books/${bookId}`, 'POST'),
    // 我要归还图书
    returnBook: (bookId) => request(`/api/users/me/books/${bookId}`, 'DELETE')
  },

  // 用户信息API
  users: {
    // 获取当前用户信息
    getMe: () => request('/api/users/me'),
    // 获取所有用户 (管理员权限)
    getAll: () => request('/api/users'),
    // 获取指定用户信息 (管理员权限)
    getById: (id) => request(`/api/users/${id}`),
    // 创建用户 (管理员权限)
    create: (userData) => request('/api/users', 'POST', userData),
    // 更新用户信息 (管理员权限)
    update: (id, userData) => request(`/api/users/${id}`, 'PUT', userData),
    // 删除用户 (管理员权限)
    delete: (id) => request(`/api/users/${id}`, 'DELETE')
  },

  // 管理员借阅管理API
  adminBorrowings: {
    // 查看用户借阅情况
    getUserBooks: (userId) => request(`/api/users/${userId}/books`),
    // 帮用户借阅图书
    borrowBookForUser: (userId, bookId) => request(`/api/users/${userId}/books/${bookId}`, 'POST'),
    // 帮用户归还图书
    returnBookForUser: (userId, bookId) => request(`/api/users/${userId}/books/${bookId}`, 'DELETE')
  },

  // 通知管理API
  notifications: {
    // 获取用户通知
    getAll: () => request('/api/notifications'),
    // 获取未读通知数量
    getUnreadCount: () => request('/api/notifications/unread/count'),
    // 标记通知已读
    markAsRead: (id) => request(`/api/notifications/${id}/read`, 'PUT'),
    // 标记所有通知已读
    markAllAsRead: () => request('/api/notifications/read-all', 'PUT'),
    // 发送系统广播 (管理员权限)
    broadcast: (notificationData) => request('/api/notifications/broadcast', 'POST', notificationData)
  },

  // Book Analytics Service API (端口9999)
  analytics: {
    // 数据收集接口
    collectBorrow: (data) => request('/api/collect/borrow', 'POST', data, 'http://localhost:9999'),
    collectReturn: (data) => request('/api/collect/return', 'POST', data, 'http://localhost:9999'),
    collectLogin: (data) => request('/api/collect/login', 'POST', data, 'http://localhost:9999'),

    // 统计查询接口
    getTodayStats: () => request('/api/stats/today', 'GET', null, 'http://localhost:9999'),
    getHotBooks: (limit = 10) => request(`/api/stats/hot-books?limit=${limit}`, 'GET', null, 'http://localhost:9999'),
    getOverview: () => request('/api/stats/overview', 'GET', null, 'http://localhost:9999'),
    getRecentDays: (days = 7) => request(`/api/stats/recent-days?days=${days}`, 'GET', null, 'http://localhost:9999'),

    // 系统管理接口
    getStatus: () => request('/api/status', 'GET', null, 'http://localhost:9999'),
    cleanup: () => request('/api/admin/cleanup', 'POST', null, 'http://localhost:9999')
  }
};
