// 用户认证模块
import api from '@/api'


// 初始状态
const state = {
  user: JSON.parse(localStorage.getItem('user')) || null,
  isAuthenticated: !!localStorage.getItem('user'),
  loading: false
}

// getters
const getters = {
  isAuthenticated: state => state.isAuthenticated,
  currentUser: state => state.user,
  isLoading: state => state.loading,
  isAdmin: state => state.user?.role === 'ROLE_ADMIN',
  isUser: state => state.user?.role === 'ROLE_USER'
}

// actions
const actions = {
  // 登录
  async login({ commit }, userData) {
    try {
      commit('SET_LOADING', true)

      // 调用后端API进行登录验证
      const response = await api.auth.login(userData)

      if (response.success && response.data) {
        const user = {
          username: response.data.username,
          role: response.data.role,
          token: response.data.token,
          refreshToken: response.data.refreshToken
        }

        // 保存到localStorage
        localStorage.setItem('user', JSON.stringify(user))

        // 提交mutation
        commit('SET_USER', user)
        commit('SET_AUTHENTICATED', true)

        return user
      } else {
        throw new Error(response.error || '登录失败')
      }
    } catch (error) {
      commit('SET_USER', null)
      commit('SET_AUTHENTICATED', false)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },

  // 注册
  async register({ commit }, userInput) {
    try {
      commit('SET_LOADING', true)

      // 调用后端API进行注册
      const response = await api.auth.register(userInput)

      if (response.success) {
        return response
      } else {
        throw new Error(response.error || '注册失败')
      }
    // eslint-disable-next-line no-useless-catch
    } catch (error) {
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },

  // 获取当前用户信息
  async fetchUserInfo({ commit }) {
    try {
      const response = await api.users.getMe()

      if (response.success && response.data) {
        const currentUser = JSON.parse(localStorage.getItem('user'))
        const updatedUser = {
          ...currentUser,
          ...response.data
        }

        localStorage.setItem('user', JSON.stringify(updatedUser))
        commit('SET_USER', updatedUser)

        return updatedUser
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    }
  },



  // 退出登录
  async logout({ commit }) {
    try {
      // 可以调用后端API通知服务器用户退出
      // await api.auth.logout()

      // 清除localStorage
      localStorage.removeItem('user')

      // 提交mutation
      commit('SET_USER', null)
      commit('SET_AUTHENTICATED', false)
      commit('SET_LOADING', false)

      return Promise.resolve()
    } catch (error) {
      // 即使后端API调用失败，也要清除本地状态
      localStorage.removeItem('user')
      commit('SET_USER', null)
      commit('SET_AUTHENTICATED', false)
      commit('SET_LOADING', false)

      console.error('退出登录时发生错误:', error)
      return Promise.resolve()
    }
  }
}

// mutations
const mutations = {
  SET_USER(state, user) {
    state.user = user
  },
  SET_AUTHENTICATED(state, isAuthenticated) {
    state.isAuthenticated = isAuthenticated
  },
  SET_LOADING(state, loading) {
    state.loading = loading
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
}
