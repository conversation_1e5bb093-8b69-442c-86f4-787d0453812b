// GitHub OAuth2认证服务
import axios from 'axios'
import { 
  GITHUB_CONFIG, 
  GITHUB_API_CONFIG, 
  GITHUB_ERROR_CODES,
  GITHUB_STORAGE_KEYS 
} from '@/config/githubConfig'

class GithubAuthService {
  constructor() {
    // 创建专用的axios实例用于GitHub API调用
    this.apiClient = axios.create({
      baseURL: GITHUB_API_CONFIG.baseUrl,
      timeout: GITHUB_API_CONFIG.timeout,
      headers: {
        'Accept': 'application/vnd.github+json',
        'X-GitHub-Api-Version': GITHUB_API_CONFIG.version,
        'User-Agent': GITHUB_API_CONFIG.userAgent
      }
    })

    // 响应拦截器
    this.apiClient.interceptors.response.use(
      response => response.data,
      error => {
        console.error('GitHub API请求错误:', error)
        return Promise.reject(this.handleError(error))
      }
    )
  }

  /**
   * 生成GitHub OAuth2授权URL
   * @returns {string} 授权URL
   */
  generateAuthUrl() {
    const params = new URLSearchParams({
      client_id: GITHUB_CONFIG.clientId,
      redirect_uri: GITHUB_CONFIG.redirectUri,
      scope: GITHUB_CONFIG.scope,
      state: GITHUB_CONFIG.state,
      allow_signup: GITHUB_CONFIG.allowSignup
    })

    // 保存state到本地存储，用于验证回调
    localStorage.setItem(GITHUB_STORAGE_KEYS.AUTH_STATE, GITHUB_CONFIG.state)

    return `${GITHUB_CONFIG.authUrl}?${params.toString()}`
  }

  /**
   * 跳转到GitHub授权页面
   */
  redirectToAuth() {
    const authUrl = this.generateAuthUrl()
    console.log('跳转到GitHub授权页面:', authUrl)
    window.location.href = authUrl
  }

  /**
   * 验证回调状态参数
   * @param {string} state 回调中的state参数
   * @returns {boolean} 验证结果
   */
  validateState(state) {
    const storedState = localStorage.getItem(GITHUB_STORAGE_KEYS.AUTH_STATE)
    return state === storedState
  }

  /**
   * 使用授权码获取访问令牌
   * @param {string} code 授权码
   * @returns {Promise<Object>} 令牌信息
   */
  async getAccessToken(code) {
    try {
      const response = await axios.post(GITHUB_CONFIG.tokenUrl, {
        client_id: GITHUB_CONFIG.clientId,
        client_secret: GITHUB_CONFIG.clientSecret,
        code: code
      }, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': GITHUB_API_CONFIG.userAgent
        }
      })

      if (response.data.access_token) {
        // 保存令牌到本地存储
        localStorage.setItem(GITHUB_STORAGE_KEYS.ACCESS_TOKEN, response.data.access_token)
        localStorage.setItem(GITHUB_STORAGE_KEYS.TOKEN_TYPE, response.data.token_type || 'bearer')
        localStorage.setItem(GITHUB_STORAGE_KEYS.SCOPE, response.data.scope || '')
        
        return {
          success: true,
          data: response.data
        }
      } else {
        throw new Error(response.data.error_description || '获取访问令牌失败')
      }
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.error_description || error.message
      }
    }
  }

  /**
   * 获取用户信息
   * @param {string} accessToken 访问令牌
   * @returns {Promise<Object>} 用户信息
   */
  async getUserInfo(accessToken) {
    try {
      const response = await this.apiClient.get('/user', {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      })

      if (response) {
        // 保存用户信息到本地存储
        localStorage.setItem(GITHUB_STORAGE_KEYS.USER_INFO, JSON.stringify(response))
        
        return {
          success: true,
          data: response
        }
      } else {
        throw new Error('获取用户信息失败')
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 获取用户邮箱信息
   * @param {string} accessToken 访问令牌
   * @returns {Promise<Object>} 邮箱信息
   */
  async getUserEmails(accessToken) {
    try {
      const response = await this.apiClient.get('/user/emails', {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      })

      return {
        success: true,
        data: response
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 验证访问令牌
   * @param {string} accessToken 访问令牌
   * @returns {Promise<Object>} 验证结果
   */
  async validateAccessToken(accessToken) {
    try {
      const response = await this.apiClient.get('/user', {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      })

      return {
        success: true,
        data: response
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 撤销访问令牌
   * @param {string} accessToken 访问令牌
   * @returns {Promise<Object>} 撤销结果
   */
  async revokeAccessToken(accessToken) {
    try {
      await axios.delete(`https://api.github.com/applications/${GITHUB_CONFIG.clientId}/token`, {
        auth: {
          username: GITHUB_CONFIG.clientId,
          password: GITHUB_CONFIG.clientSecret
        },
        data: {
          access_token: accessToken
        },
        headers: {
          'Accept': 'application/vnd.github+json',
          'X-GitHub-Api-Version': GITHUB_API_CONFIG.version
        }
      })

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 清除本地存储的GitHub认证信息
   */
  clearAuthData() {
    localStorage.removeItem(GITHUB_STORAGE_KEYS.ACCESS_TOKEN)
    localStorage.removeItem(GITHUB_STORAGE_KEYS.TOKEN_TYPE)
    localStorage.removeItem(GITHUB_STORAGE_KEYS.SCOPE)
    localStorage.removeItem(GITHUB_STORAGE_KEYS.USER_INFO)
    localStorage.removeItem(GITHUB_STORAGE_KEYS.AUTH_STATE)
  }

  /**
   * 获取本地存储的访问令牌
   * @returns {string|null} 访问令牌
   */
  getStoredAccessToken() {
    return localStorage.getItem(GITHUB_STORAGE_KEYS.ACCESS_TOKEN)
  }

  /**
   * 获取本地存储的用户信息
   * @returns {Object|null} 用户信息
   */
  getStoredUserInfo() {
    const userInfo = localStorage.getItem(GITHUB_STORAGE_KEYS.USER_INFO)
    return userInfo ? JSON.parse(userInfo) : null
  }

  /**
   * 处理API错误
   * @param {Error} error 错误对象
   * @returns {Error} 处理后的错误
   */
  handleError(error) {
    if (error.response?.data?.error) {
      const errorCode = error.response.data.error
      const errorMessage = GITHUB_ERROR_CODES[errorCode] || error.response.data.error_description || '未知错误'
      return new Error(errorMessage)
    }
    
    if (error.response?.data?.message) {
      return new Error(error.response.data.message)
    }
    
    return error
  }

  /**
   * 完整的GitHub OAuth2登录流程
   * @param {string} code 授权码
   * @param {string} state 状态参数
   * @returns {Promise<Object>} 登录结果
   */
  async completeLogin(code, state) {
    try {
      // 1. 验证state参数
      if (!this.validateState(state)) {
        throw new Error('状态参数验证失败，可能存在安全风险')
      }

      // 2. 获取访问令牌
      const tokenResult = await this.getAccessToken(code)
      if (!tokenResult.success) {
        throw new Error(tokenResult.error)
      }

      // 3. 获取用户信息
      const userInfoResult = await this.getUserInfo(tokenResult.data.access_token)
      if (!userInfoResult.success) {
        throw new Error(userInfoResult.error)
      }

      // 4. 获取用户邮箱信息（如果需要）
      let emailsResult = null
      if (tokenResult.data.scope && tokenResult.data.scope.includes('user:email')) {
        emailsResult = await this.getUserEmails(tokenResult.data.access_token)
      }

      // 5. 清除临时状态
      localStorage.removeItem(GITHUB_STORAGE_KEYS.AUTH_STATE)

      return {
        success: true,
        data: {
          token: tokenResult.data,
          userInfo: userInfoResult.data,
          emails: emailsResult?.data || []
        }
      }
    } catch (error) {
      this.clearAuthData()
      return {
        success: false,
        error: error.message
      }
    }
  }
}

// 导出单例实例
export default new GithubAuthService()
