<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GitHub OAuth2 认证 - 图书管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 450px;
            width: 100%;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .header {
            background: #24292f;
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .github-logo {
            width: 64px;
            height: 64px;
            margin: 0 auto 15px;
            fill: white;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .header p {
            opacity: 0.8;
            font-size: 14px;
        }

        .content {
            padding: 40px 30px;
        }

        .login-section {
            text-align: center;
        }

        .login-section h2 {
            color: #24292f;
            font-size: 20px;
            margin-bottom: 10px;
        }

        .login-section .subtitle {
            color: #586069;
            font-size: 14px;
            margin-bottom: 30px;
            line-height: 1.5;
        }

        .github-btn {
            background: #24292f;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 14px 24px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            transition: all 0.3s ease;
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }

        .github-btn:hover {
            background: #1c2128;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(36, 41, 47, 0.3);
        }

        .github-btn:active {
            transform: translateY(0);
        }

        .github-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        .github-btn:hover::before {
            left: 100%;
        }

        .github-btn svg {
            width: 20px;
            height: 20px;
            fill: currentColor;
        }

        .loading {
            display: none;
            align-items: center;
            justify-content: center;
            gap: 10px;
            color: #586069;
            margin-top: 20px;
        }

        .loading.show {
            display: flex;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #e1e4e8;
            border-top: 2px solid #586069;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .features {
            margin-top: 30px;
            padding-top: 30px;
            border-top: 1px solid #e1e4e8;
        }

        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            color: #586069;
            font-size: 14px;
        }

        .feature-item:last-child {
            margin-bottom: 0;
        }

        .feature-icon {
            width: 16px;
            height: 16px;
            margin-right: 10px;
            color: #28a745;
        }

        .callback-section {
            display: none;
            text-align: center;
        }

        .callback-section.show {
            display: block;
        }

        .success-icon, .error-icon {
            width: 48px;
            height: 48px;
            margin: 0 auto 20px;
        }

        .success-icon {
            color: #28a745;
        }

        .error-icon {
            color: #dc3545;
        }

        .user-info {
            background: #f6f8fa;
            border: 1px solid #d0d7de;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            text-align: left;
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: 3px solid #d0d7de;
            margin: 0 auto 15px;
            display: block;
        }

        .user-name {
            font-size: 18px;
            font-weight: 600;
            color: #24292f;
            margin-bottom: 5px;
            text-align: center;
        }

        .user-login {
            color: #586069;
            font-size: 14px;
            text-align: center;
            margin-bottom: 15px;
        }

        .user-stats {
            display: flex;
            justify-content: space-around;
            padding-top: 15px;
            border-top: 1px solid #d0d7de;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 16px;
            font-weight: 600;
            color: #24292f;
            display: block;
        }

        .stat-label {
            font-size: 12px;
            color: #586069;
            margin-top: 2px;
        }

        .error-message {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 6px;
            padding: 12px;
            margin: 20px 0;
            font-size: 14px;
        }

        .retry-btn {
            background: #0969da;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 10px 20px;
            font-size: 14px;
            cursor: pointer;
            margin-top: 15px;
            transition: background 0.2s;
        }

        .retry-btn:hover {
            background: #0860ca;
        }

        .footer {
            text-align: center;
            padding: 20px;
            color: #586069;
            font-size: 12px;
            border-top: 1px solid #e1e4e8;
            background: #f6f8fa;
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .container {
                margin: 10px;
                max-width: none;
            }

            .header, .content {
                padding: 25px 20px;
            }

            .header h1 {
                font-size: 20px;
            }

            .github-btn {
                padding: 12px 20px;
                font-size: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <div class="header-content">
                <svg class="github-logo" viewBox="0 0 1024 1024">
                    <path d="M512 12.64c-282.752 0-512 229.216-512 512 0 226.208 146.688 418.144 350.08 485.824 25.6 4.736 35.008-11.104 35.008-24.64 0-12.192-0.48-52.544-0.704-95.328-142.464 30.976-172.512-60.416-172.512-60.416-23.296-59.168-56.832-74.912-56.832-74.912-46.464-31.776 3.52-31.136 3.52-31.136 51.392 3.616 78.464 52.768 78.464 52.768 45.664 78.272 119.776 55.648 148.992 42.56 4.576-33.088 17.856-55.68 32.512-68.48-113.728-12.928-233.216-56.864-233.216-253.024 0-55.904 19.936-101.568 52.672-137.408-5.312-12.896-22.848-64.96 4.96-135.488 0 0 42.88-13.76 140.8 52.48 40.832-11.36 84.64-17.024 128.16-17.248 43.488 0.192 87.328 5.888 128.256 17.248 97.728-66.24 140.64-52.48 140.64-52.48 27.872 70.528 10.336 122.592 5.024 135.488 32.832 35.84 52.608 81.504 52.608 137.408 0 196.64-119.776 239.936-233.856 252.64 18.368 15.904 34.72 47.04 34.72 94.816 0 68.512-0.608 123.648-0.608 140.512 0 13.632 9.216 29.6 35.168 24.576C877.472 942.08 1024 750.208 1024 524.64c0-282.784-229.248-512-512-512z"/>
                </svg>
                <h1>GitHub OAuth2 认证</h1>
                <p>安全便捷的第三方登录方式</p>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="content">
            <!-- 登录界面 -->
            <div class="login-section" id="loginSection">
                <h2>使用 GitHub 账号登录</h2>
                <p class="subtitle">
                    点击下方按钮将跳转到 GitHub 进行安全认证<br>
                    授权后将自动返回到图书管理系统
                </p>

                <button class="github-btn" id="githubLoginBtn" onclick="startGithubAuth()">
                    <svg viewBox="0 0 1024 1024">
                        <path d="M512 12.64c-282.752 0-512 229.216-512 512 0 226.208 146.688 418.144 350.08 485.824 25.6 4.736 35.008-11.104 35.008-24.64 0-12.192-0.48-52.544-0.704-95.328-142.464 30.976-172.512-60.416-172.512-60.416-23.296-59.168-56.832-74.912-56.832-74.912-46.464-31.776 3.52-31.136 3.52-31.136 51.392 3.616 78.464 52.768 78.464 52.768 45.664 78.272 119.776 55.648 148.992 42.56 4.576-33.088 17.856-55.68 32.512-68.48-113.728-12.928-233.216-56.864-233.216-253.024 0-55.904 19.936-101.568 52.672-137.408-5.312-12.896-22.848-64.96 4.96-135.488 0 0 42.88-13.76 140.8 52.48 40.832-11.36 84.64-17.024 128.16-17.248 43.488 0.192 87.328 5.888 128.256 17.248 97.728-66.24 140.64-52.48 140.64-52.48 27.872 70.528 10.336 122.592 5.024 135.488 32.832 35.84 52.608 81.504 52.608 137.408 0 196.64-119.776 239.936-233.856 252.64 18.368 15.904 34.72 47.04 34.72 94.816 0 68.512-0.608 123.648-0.608 140.512 0 13.632 9.216 29.6 35.168 24.576C877.472 942.08 1024 750.208 1024 524.64c0-282.784-229.248-512-512-512z"/>
                    </svg>
                    <span id="btnText">使用 GitHub 登录</span>
                </button>

                <div class="loading" id="loadingIndicator">
                    <div class="spinner"></div>
                    <span>正在跳转到 GitHub...</span>
                </div>

                <div class="features">
                    <div class="feature-item">
                        <svg class="feature-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span>安全的 OAuth2 认证协议</span>
                    </div>
                    <div class="feature-item">
                        <svg class="feature-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span>无需注册新账号</span>
                    </div>
                    <div class="feature-item">
                        <svg class="feature-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span>快速便捷的登录体验</span>
                    </div>
                </div>
            </div>

            <!-- 回调处理界面 -->
            <div class="callback-section" id="callbackSection">
                <!-- 处理中状态 -->
                <div id="processingState">
                    <div class="spinner" style="width: 48px; height: 48px; margin: 0 auto 20px;"></div>
                    <h2>正在处理认证信息...</h2>
                    <p id="processingMessage">正在验证授权信息</p>
                </div>

                <!-- 成功状态 -->
                <div id="successState" style="display: none;">
                    <svg class="success-icon" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    <h2>登录成功！</h2>
                    <p>欢迎回来，正在跳转到系统...</p>
                    <div class="user-info" id="userInfoDisplay" style="display: none;">
                        <img class="user-avatar" id="userAvatar" src="" alt="用户头像">
                        <div class="user-name" id="userName"></div>
                        <div class="user-login" id="userLogin"></div>
                        <div class="user-stats">
                            <div class="stat-item">
                                <span class="stat-number" id="userRepos">0</span>
                                <div class="stat-label">仓库</div>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number" id="userFollowers">0</span>
                                <div class="stat-label">关注者</div>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number" id="userFollowing">0</span>
                                <div class="stat-label">关注</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 错误状态 -->
                <div id="errorState" style="display: none;">
                    <svg class="error-icon" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                    </svg>
                    <h2>认证失败</h2>
                    <div class="error-message" id="errorMessage"></div>
                    <button class="retry-btn" onclick="retryAuth()">重新认证</button>
                </div>
            </div>
        </div>

        <!-- 页面底部 -->
        <div class="footer">
            <p>© 2024 图书管理系统. 使用 GitHub OAuth2 安全认证</p>
        </div>
    </div>

    <script>
        // GitHub OAuth2 配置
        const GITHUB_CONFIG = {
            clientId: 'Ov23liCALoME7prsT03j',
            clientSecret: '507ddea45ea3d48fc99a2a479a8521daf0aafa32',
            authUrl: 'https://github.com/login/oauth/authorize',
            tokenUrl: 'https://github.com/login/oauth/access_token',
            userInfoUrl: 'https://api.github.com/user',
            userEmailsUrl: 'https://api.github.com/user/emails',
            redirectUri: window.location.origin + '/public/github-oauth.html',
            scope: 'user:email',
            state: 'library_system_auth_' + Date.now(),
            allowSignup: true
        };

        // 错误码映射
        const ERROR_MESSAGES = {
            'access_denied': '用户拒绝授权',
            'invalid_request': '请求参数错误',
            'invalid_client': '客户端认证失败',
            'invalid_grant': '授权码无效或已过期',
            'unauthorized_client': '客户端未授权',
            'unsupported_grant_type': '不支持的授权类型',
            'invalid_scope': '请求的权限范围无效',
            'temporarily_unavailable': '服务暂时不可用',
            'redirect_uri_mismatch': '回调地址不匹配',
            'bad_verification_code': '验证码错误',
            'incorrect_client_credentials': '客户端凭证错误'
        };

        // 页面加载时检查是否为回调
        window.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const code = urlParams.get('code');
            const error = urlParams.get('error');
            const state = urlParams.get('state');

            if (code || error) {
                // 这是回调页面
                showCallbackSection();
                handleCallback(code, error, state, urlParams.get('error_description'));
            } else {
                // 这是登录页面
                showLoginSection();
            }
        });

        // 显示登录界面
        function showLoginSection() {
            document.getElementById('loginSection').style.display = 'block';
            document.getElementById('callbackSection').style.display = 'none';
        }

        // 显示回调处理界面
        function showCallbackSection() {
            document.getElementById('loginSection').style.display = 'none';
            document.getElementById('callbackSection').style.display = 'block';
        }

        // 开始GitHub认证
        function startGithubAuth() {
            const btn = document.getElementById('githubLoginBtn');
            const btnText = document.getElementById('btnText');
            const loading = document.getElementById('loadingIndicator');

            // 显示加载状态
            btn.disabled = true;
            btnText.textContent = '正在跳转...';
            loading.classList.add('show');

            // 保存state到localStorage
            localStorage.setItem('github_auth_state', GITHUB_CONFIG.state);

            // 构建授权URL
            const authUrl = buildAuthUrl();

            // 延迟跳转，让用户看到加载状态
            setTimeout(() => {
                window.location.href = authUrl;
            }, 1000);
        }

        // 构建GitHub授权URL
        function buildAuthUrl() {
            const params = new URLSearchParams({
                client_id: GITHUB_CONFIG.clientId,
                redirect_uri: GITHUB_CONFIG.redirectUri,
                scope: GITHUB_CONFIG.scope,
                state: GITHUB_CONFIG.state,
                allow_signup: GITHUB_CONFIG.allowSignup
            });

            return `${GITHUB_CONFIG.authUrl}?${params.toString()}`;
        }

        // 处理GitHub回调
        async function handleCallback(code, error, state, errorDescription) {
            const processingMessage = document.getElementById('processingMessage');

            try {
                // 检查是否有错误
                if (error) {
                    throw new Error(ERROR_MESSAGES[error] || errorDescription || '认证失败');
                }

                // 检查必需参数
                if (!code) {
                    throw new Error('未收到授权码，请重新登录');
                }

                // 验证state参数
                const storedState = localStorage.getItem('github_auth_state');
                if (!state || state !== storedState) {
                    throw new Error('状态参数验证失败，可能存在安全风险');
                }

                // 获取访问令牌
                processingMessage.textContent = '正在获取访问令牌...';
                const tokenData = await getAccessToken(code);

                // 获取用户信息
                processingMessage.textContent = '正在获取用户信息...';
                const userInfo = await getUserInfo(tokenData.access_token);

                // 显示成功状态
                showSuccessState(userInfo);

                // 清理localStorage
                localStorage.removeItem('github_auth_state');

                // 延迟跳转（实际应用中可以跳转到主系统）
                setTimeout(() => {
                    alert('认证成功！在实际应用中，这里会跳转到图书管理系统主页。');
                    // window.location.href = '/dashboard'; // 实际跳转地址
                }, 3000);

            } catch (err) {
                console.error('GitHub认证失败:', err);
                showErrorState(err.message);
            }
        }

        // 获取访问令牌
        async function getAccessToken(code) {
            const response = await fetch(GITHUB_CONFIG.tokenUrl, {
                method: 'POST',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    client_id: GITHUB_CONFIG.clientId,
                    client_secret: GITHUB_CONFIG.clientSecret,
                    code: code
                })
            });

            const data = await response.json();

            if (data.error) {
                throw new Error(ERROR_MESSAGES[data.error] || data.error_description || '获取访问令牌失败');
            }

            if (!data.access_token) {
                throw new Error('未收到访问令牌');
            }

            return data;
        }

        // 获取用户信息
        async function getUserInfo(accessToken) {
            const response = await fetch(GITHUB_CONFIG.userInfoUrl, {
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Accept': 'application/vnd.github+json',
                    'X-GitHub-Api-Version': '2022-11-28'
                }
            });

            if (!response.ok) {
                throw new Error('获取用户信息失败');
            }

            const userInfo = await response.json();
            return userInfo;
        }

        // 显示成功状态
        function showSuccessState(userInfo) {
            document.getElementById('processingState').style.display = 'none';
            document.getElementById('successState').style.display = 'block';
            document.getElementById('errorState').style.display = 'none';

            // 显示用户信息
            const userInfoDisplay = document.getElementById('userInfoDisplay');
            const userAvatar = document.getElementById('userAvatar');
            const userName = document.getElementById('userName');
            const userLogin = document.getElementById('userLogin');
            const userRepos = document.getElementById('userRepos');
            const userFollowers = document.getElementById('userFollowers');
            const userFollowing = document.getElementById('userFollowing');

            userAvatar.src = userInfo.avatar_url;
            userName.textContent = userInfo.name || userInfo.login;
            userLogin.textContent = '@' + userInfo.login;
            userRepos.textContent = userInfo.public_repos || 0;
            userFollowers.textContent = userInfo.followers || 0;
            userFollowing.textContent = userInfo.following || 0;

            userInfoDisplay.style.display = 'block';

            // 保存用户信息到localStorage（实际应用中可能需要发送到后端）
            localStorage.setItem('github_user_info', JSON.stringify(userInfo));
        }

        // 显示错误状态
        function showErrorState(errorMessage) {
            document.getElementById('processingState').style.display = 'none';
            document.getElementById('successState').style.display = 'none';
            document.getElementById('errorState').style.display = 'block';

            document.getElementById('errorMessage').textContent = errorMessage;
        }

        // 重新认证
        function retryAuth() {
            // 清理localStorage
            localStorage.removeItem('github_auth_state');
            localStorage.removeItem('github_user_info');

            // 重新加载页面，回到登录界面
            window.location.href = window.location.pathname;
        }

        // 工具函数：格式化数字
        function formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        }

        // 工具函数：检查浏览器支持
        function checkBrowserSupport() {
            const isSupported = !!(window.fetch && window.localStorage && window.URLSearchParams);

            if (!isSupported) {
                showErrorState('您的浏览器版本过低，不支持GitHub OAuth2认证。请升级浏览器后重试。');
                return false;
            }

            return true;
        }

        // 页面初始化时检查浏览器支持
        window.addEventListener('DOMContentLoaded', function() {
            if (!checkBrowserSupport()) {
                return;
            }
        });

        // 添加键盘事件支持
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Enter') {
                const loginSection = document.getElementById('loginSection');
                if (loginSection.style.display !== 'none') {
                    startGithubAuth();
                }
            }
        });

        // 添加错误处理
        window.addEventListener('error', function(event) {
            console.error('页面错误:', event.error);
            showErrorState('页面发生错误，请刷新后重试');
        });

        // 添加网络状态检查
        window.addEventListener('online', function() {
            console.log('网络连接已恢复');
        });

        window.addEventListener('offline', function() {
            console.log('网络连接已断开');
            showErrorState('网络连接已断开，请检查网络后重试');
        });
    </script>
</body>
</html>
