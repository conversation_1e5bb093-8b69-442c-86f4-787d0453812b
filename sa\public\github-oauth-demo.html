<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GitHub OAuth2 认证演示 - 图书管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 450px;
            width: 100%;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .header {
            background: #24292f;
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .github-logo {
            width: 64px;
            height: 64px;
            margin: 0 auto 15px;
            fill: white;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .header p {
            opacity: 0.8;
            font-size: 14px;
        }

        .content {
            padding: 40px 30px;
        }

        .demo-notice {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 25px;
            color: #856404;
            font-size: 14px;
            line-height: 1.5;
        }

        .demo-notice strong {
            color: #533f03;
        }

        .login-section {
            text-align: center;
        }

        .login-section h2 {
            color: #24292f;
            font-size: 20px;
            margin-bottom: 10px;
        }

        .login-section .subtitle {
            color: #586069;
            font-size: 14px;
            margin-bottom: 30px;
            line-height: 1.5;
        }

        .github-btn {
            background: #24292f;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 14px 24px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            transition: all 0.3s ease;
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }

        .github-btn:hover {
            background: #1c2128;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(36, 41, 47, 0.3);
        }

        .github-btn svg {
            width: 20px;
            height: 20px;
            fill: currentColor;
        }

        .features {
            margin-top: 30px;
            padding-top: 30px;
            border-top: 1px solid #e1e4e8;
        }

        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            color: #586069;
            font-size: 14px;
        }

        .feature-item:last-child {
            margin-bottom: 0;
        }

        .feature-icon {
            width: 16px;
            height: 16px;
            margin-right: 10px;
            color: #28a745;
        }

        .code-example {
            background: #f6f8fa;
            border: 1px solid #d0d7de;
            border-radius: 8px;
            padding: 20px;
            margin-top: 25px;
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
            font-size: 13px;
            line-height: 1.5;
            overflow-x: auto;
        }

        .code-example h3 {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            font-size: 14px;
            font-weight: 600;
            color: #24292f;
            margin-bottom: 10px;
        }

        .code-example pre {
            margin: 0;
            color: #24292f;
        }

        .footer {
            text-align: center;
            padding: 20px;
            color: #586069;
            font-size: 12px;
            border-top: 1px solid #e1e4e8;
            background: #f6f8fa;
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .container {
                margin: 10px;
                max-width: none;
            }

            .header, .content {
                padding: 25px 20px;
            }

            .header h1 {
                font-size: 20px;
            }

            .github-btn {
                padding: 12px 20px;
                font-size: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <svg class="github-logo" viewBox="0 0 1024 1024">
                <path d="M512 12.64c-282.752 0-512 229.216-512 512 0 226.208 146.688 418.144 350.08 485.824 25.6 4.736 35.008-11.104 35.008-24.64 0-12.192-0.48-52.544-0.704-95.328-142.464 30.976-172.512-60.416-172.512-60.416-23.296-59.168-56.832-74.912-56.832-74.912-46.464-31.776 3.52-31.136 3.52-31.136 51.392 3.616 78.464 52.768 78.464 52.768 45.664 78.272 119.776 55.648 148.992 42.56 4.576-33.088 17.856-55.68 32.512-68.48-113.728-12.928-233.216-56.864-233.216-253.024 0-55.904 19.936-101.568 52.672-137.408-5.312-12.896-22.848-64.96 4.96-135.488 0 0 42.88-13.76 140.8 52.48 40.832-11.36 84.64-17.024 128.16-17.248 43.488 0.192 87.328 5.888 128.256 17.248 97.728-66.24 140.64-52.48 140.64-52.48 27.872 70.528 10.336 122.592 5.024 135.488 32.832 35.84 52.608 81.504 52.608 137.408 0 196.64-119.776 239.936-233.856 252.64 18.368 15.904 34.72 47.04 34.72 94.816 0 68.512-0.608 123.648-0.608 140.512 0 13.632 9.216 29.6 35.168 24.576C877.472 942.08 1024 750.208 1024 524.64c0-282.784-229.248-512-512-512z"/>
            </svg>
            <h1>GitHub OAuth2 认证演示</h1>
            <p>安全便捷的第三方登录方式</p>
        </div>

        <!-- 主要内容区域 -->
        <div class="content">
            <!-- 演示说明 -->
            <div class="demo-notice">
                <strong>演示说明：</strong> 这是一个GitHub OAuth2认证的演示页面。在生产环境中，客户端密钥应该保存在后端服务器中，而不是暴露在前端代码中。
            </div>

            <!-- 登录界面 -->
            <div class="login-section">
                <h2>使用 GitHub 账号登录</h2>
                <p class="subtitle">
                    点击下方按钮将跳转到 GitHub 进行安全认证<br>
                    授权后将自动返回到图书管理系统
                </p>

                <button class="github-btn" onclick="startGithubAuth()">
                    <svg viewBox="0 0 1024 1024">
                        <path d="M512 12.64c-282.752 0-512 229.216-512 512 0 226.208 146.688 418.144 350.08 485.824 25.6 4.736 35.008-11.104 35.008-24.64 0-12.192-0.48-52.544-0.704-95.328-142.464 30.976-172.512-60.416-172.512-60.416-23.296-59.168-56.832-74.912-56.832-74.912-46.464-31.776 3.52-31.136 3.52-31.136 51.392 3.616 78.464 52.768 78.464 52.768 45.664 78.272 119.776 55.648 148.992 42.56 4.576-33.088 17.856-55.68 32.512-68.48-113.728-12.928-233.216-56.864-233.216-253.024 0-55.904 19.936-101.568 52.672-137.408-5.312-12.896-22.848-64.96 4.96-135.488 0 0 42.88-13.76 140.8 52.48 40.832-11.36 84.64-17.024 128.16-17.248 43.488 0.192 87.328 5.888 128.256 17.248 97.728-66.24 140.64-52.48 140.64-52.48 27.872 70.528 10.336 122.592 5.024 135.488 32.832 35.84 52.608 81.504 52.608 137.408 0 196.64-119.776 239.936-233.856 252.64 18.368 15.904 34.72 47.04 34.72 94.816 0 68.512-0.608 123.648-0.608 140.512 0 13.632 9.216 29.6 35.168 24.576C877.472 942.08 1024 750.208 1024 524.64c0-282.784-229.248-512-512-512z"/>
                    </svg>
                    <span>GitHub OAuth2 演示</span>
                </button>

                <div class="features">
                    <div class="feature-item">
                        <svg class="feature-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span>安全的 OAuth2 认证协议</span>
                    </div>
                    <div class="feature-item">
                        <svg class="feature-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span>无需注册新账号</span>
                    </div>
                    <div class="feature-item">
                        <svg class="feature-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span>快速便捷的登录体验</span>
                    </div>
                </div>

                <!-- 代码示例 -->
                <div class="code-example">
                    <h3>GitHub OAuth2 授权URL示例：</h3>
                    <pre id="authUrlExample">正在生成授权URL...</pre>
                </div>
            </div>
        </div>

        <!-- 页面底部 -->
        <div class="footer">
            <p>© 2024 图书管理系统. GitHub OAuth2 认证演示</p>
        </div>
    </div>

    <script>
        // GitHub OAuth2 配置（演示用）
        const GITHUB_CONFIG = {
            clientId: 'Ov23liwEIqDsrO0JoRl7',
            authUrl: 'https://github.com/login/oauth/authorize',
            redirectUri: window.location.origin + '/public/github-oauth.html',
            scope: 'user:email',
            state: 'library_system_auth_' + Date.now(),
            allowSignup: true
        };

        // 页面加载时生成授权URL示例
        window.addEventListener('DOMContentLoaded', function() {
            generateAuthUrlExample();
        });

        // 生成授权URL示例
        function generateAuthUrlExample() {
            const params = new URLSearchParams({
                client_id: GITHUB_CONFIG.clientId,
                redirect_uri: GITHUB_CONFIG.redirectUri,
                scope: GITHUB_CONFIG.scope,
                state: GITHUB_CONFIG.state,
                allow_signup: GITHUB_CONFIG.allowSignup
            });

            const authUrl = `${GITHUB_CONFIG.authUrl}?${params.toString()}`;
            document.getElementById('authUrlExample').textContent = authUrl;
        }

        // 开始GitHub认证演示
        function startGithubAuth() {
            // 保存state到localStorage
            localStorage.setItem('github_auth_state', GITHUB_CONFIG.state);

            // 构建授权URL
            const params = new URLSearchParams({
                client_id: GITHUB_CONFIG.clientId,
                redirect_uri: GITHUB_CONFIG.redirectUri,
                scope: GITHUB_CONFIG.scope,
                state: GITHUB_CONFIG.state,
                allow_signup: GITHUB_CONFIG.allowSignup
            });

            const authUrl = `${GITHUB_CONFIG.authUrl}?${params.toString()}`;

            // 显示确认对话框
            const confirmed = confirm(
                '这将跳转到GitHub进行OAuth2认证演示。\n\n' +
                '注意：这只是一个演示，实际的令牌交换需要在后端服务器中完成。\n\n' +
                '是否继续？'
            );

            if (confirmed) {
                // 跳转到GitHub授权页面
                window.location.href = authUrl;
            }
        }

        // 添加键盘事件支持
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Enter') {
                startGithubAuth();
            }
        });
    </script>
</body>
</html>
