<template>
  <div class="github-callback-container">
    <div class="callback-content">
      <div class="loading-section" v-if="loading">
        <el-icon class="loading-icon" :size="48">
          <Loading />
        </el-icon>
        <h2>正在处理GitHub登录...</h2>
        <p>{{ loadingMessage }}</p>
      </div>

      <div class="success-section" v-else-if="success">
        <el-icon class="success-icon" :size="48">
          <SuccessFilled />
        </el-icon>
        <h2>登录成功！</h2>
        <p>欢迎 {{ userInfo?.name || userInfo?.login }}，正在跳转到系统...</p>
        <div class="user-preview" v-if="userInfo">
          <img :src="userInfo.avatar_url" :alt="userInfo.login" class="avatar">
          <div class="user-details">
            <div class="username">@{{ userInfo.login }}</div>
            <div class="user-stats">
              <span>{{ userInfo.public_repos }} 仓库</span>
              <span>{{ userInfo.followers }} 关注者</span>
            </div>
          </div>
        </div>
      </div>

      <div class="error-section" v-else-if="error">
        <el-icon class="error-icon" :size="48">
          <CircleCloseFilled />
        </el-icon>
        <h2>登录失败</h2>
        <p class="error-message">{{ errorMessage }}</p>
        <div class="error-actions">
          <el-button type="primary" @click="retryLogin">重新登录</el-button>
          <el-button @click="goToLogin">返回登录页</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useStore } from 'vuex'
import { ElMessage } from 'element-plus'
import { Loading, SuccessFilled, CircleCloseFilled } from '@element-plus/icons-vue'
import githubAuth from '@/services/githubAuth'
import { parseHashParams, handleGithubLoginError, logGithubEvent } from '@/utils/githubUtils'

export default {
  name: 'GithubCallback',
  components: {
    Loading,
    SuccessFilled,
    CircleCloseFilled
  },
  setup() {
    const router = useRouter()
    const route = useRoute()
    const store = useStore()

    const loading = ref(true)
    const success = ref(false)
    const error = ref(false)
    const loadingMessage = ref('正在验证授权信息...')
    const errorMessage = ref('')
    const userInfo = ref(null)

    // 处理GitHub OAuth2回调
    const handleCallback = async () => {
      try {
        logGithubEvent('callback_start', { url: window.location.href })

        // 从URL中获取授权码和状态参数
        let code, state, errorCode, errorDescription

        // 检查URL参数（query string）
        code = route.query.code
        state = route.query.state
        errorCode = route.query.error
        errorDescription = route.query.error_description

        // 如果query中没有，检查hash参数
        if (!code && !errorCode) {
          const hashParams = parseHashParams()
          code = hashParams.code
          state = hashParams.state
          errorCode = hashParams.error
          errorDescription = hashParams.error_description
        }

        // 检查是否有错误
        if (errorCode) {
          throw new Error(handleGithubLoginError(errorCode) + (errorDescription ? `: ${errorDescription}` : ''))
        }

        // 检查必需参数
        if (!code) {
          throw new Error('未收到授权码，请重新登录')
        }

        if (!state) {
          throw new Error('缺少状态参数，请重新登录')
        }

        logGithubEvent('callback_params_received', { code: code.substring(0, 10) + '...', state })

        // 更新加载消息
        loadingMessage.value = '正在获取访问令牌...'

        // 完成GitHub登录流程
        const loginResult = await githubAuth.completeLogin(code, state)
        
        if (!loginResult.success) {
          throw new Error(loginResult.error)
        }

        logGithubEvent('github_login_success', { 
          userInfo: loginResult.data.userInfo 
        })

        // 更新加载消息
        loadingMessage.value = '正在同步用户信息...'

        // 调用Vuex的GitHub登录action
        const user = await store.dispatch('auth/githubLogin', {
          githubToken: loginResult.data.token,
          githubUserInfo: loginResult.data.userInfo,
          githubEmails: loginResult.data.emails
        })

        userInfo.value = loginResult.data.userInfo
        success.value = true
        loading.value = false

        logGithubEvent('system_login_success', { user })

        // 显示成功消息
        ElMessage.success('GitHub登录成功！')

        // 延迟跳转到主页
        setTimeout(() => {
          router.push('/')
        }, 2000)

      } catch (err) {
        console.error('GitHub登录回调处理失败:', err)
        logGithubEvent('callback_error', { error: err.message })
        
        error.value = true
        loading.value = false
        errorMessage.value = err.message || '登录处理失败，请重试'
        
        ElMessage.error(errorMessage.value)
      }
    }

    // 重新登录
    const retryLogin = () => {
      logGithubEvent('retry_login')
      githubAuth.clearAuthData()
      githubAuth.redirectToAuth()
    }

    // 返回登录页
    const goToLogin = () => {
      logGithubEvent('go_to_login')
      githubAuth.clearAuthData()
      router.push('/login')
    }

    // 组件挂载时处理回调
    onMounted(() => {
      handleCallback()
    })

    return {
      loading,
      success,
      error,
      loadingMessage,
      errorMessage,
      userInfo,
      retryLogin,
      goToLogin
    }
  }
}
</script>

<style scoped>
.github-callback-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f7fa;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000;
}

.callback-content {
  text-align: center;
  padding: 40px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  max-width: 450px;
  width: 90%;
}

.loading-section,
.success-section,
.error-section {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-icon {
  color: #24292f;
  animation: rotate 2s linear infinite;
}

.success-icon {
  color: #28a745;
}

.error-icon {
  color: #dc3545;
}

.loading-section h2,
.success-section h2,
.error-section h2 {
  margin: 20px 0 10px 0;
  color: #24292f;
  font-size: 20px;
}

.loading-section p,
.success-section p {
  color: #586069;
  margin-bottom: 0;
}

.error-message {
  color: #dc3545;
  margin-bottom: 20px;
  word-break: break-word;
}

.error-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  flex-wrap: wrap;
}

.user-preview {
  display: flex;
  align-items: center;
  margin-top: 20px;
  padding: 15px;
  background: #f6f8fa;
  border-radius: 8px;
  border: 1px solid #d0d7de;
}

.avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  margin-right: 12px;
  border: 2px solid #d0d7de;
}

.user-details {
  text-align: left;
  flex: 1;
}

.username {
  font-weight: 600;
  color: #24292f;
  margin-bottom: 4px;
}

.user-stats {
  font-size: 12px;
  color: #656d76;
}

.user-stats span {
  margin-right: 12px;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .callback-content {
    padding: 30px 20px;
  }
  
  .error-actions {
    flex-direction: column;
  }
  
  .error-actions .el-button {
    width: 100%;
  }

  .user-preview {
    flex-direction: column;
    text-align: center;
  }

  .avatar {
    margin-right: 0;
    margin-bottom: 8px;
  }

  .user-details {
    text-align: center;
  }
}
</style>
