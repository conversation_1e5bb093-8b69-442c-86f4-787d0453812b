// GitHub OAuth2配置
export const GITHUB_CONFIG = {
  // GitHub应用配置
  clientId: '********************', // 请替换为您的GitHub应用Client ID
  clientSecret: '507ddea45ea3d48fc99a2a479a8521daf0aafa32', // 请替换为您的GitHub应用Client Secret
  
  // OAuth2授权相关配置
  authUrl: 'https://github.com/login/oauth/authorize',
  tokenUrl: 'https://github.com/login/oauth/access_token',
  userInfoUrl: 'https://api.github.com/user',
  userEmailsUrl: 'https://api.github.com/user/emails',
  
  // 回调地址配置
  redirectUri: window.location.origin + '/#/github/callback',
  
  // 授权范围
  scope: 'user:email',
  
  // 响应类型
  responseType: 'code',
  
  // 状态参数（用于防止CSRF攻击）
  state: 'library_system_auth',
  
  // 允许注册
  allowSignup: true
}

// GitHub API配置
export const GITHUB_API_CONFIG = {
  baseUrl: 'https://api.github.com',
  version: '2022-11-28', // GitHub API版本
  timeout: 10000,
  userAgent: 'Library-Management-System/1.0'
}

// 错误码映射
export const GITHUB_ERROR_CODES = {
  'invalid_request': '请求参数错误',
  'invalid_client': '客户端认证失败',
  'invalid_grant': '授权码无效或已过期',
  'unauthorized_client': '客户端未授权',
  'unsupported_grant_type': '不支持的授权类型',
  'invalid_scope': '请求的权限范围无效',
  'access_denied': '用户拒绝授权',
  'temporarily_unavailable': '服务暂时不可用',
  'redirect_uri_mismatch': '回调地址不匹配',
  'bad_verification_code': '验证码错误',
  'incorrect_client_credentials': '客户端凭证错误',
  'device_flow_disabled': '设备流程已禁用',
  'app_suspended': '应用已被暂停',
  'rate_limit_exceeded': '请求频率超限'
}

// 本地存储键名
export const GITHUB_STORAGE_KEYS = {
  ACCESS_TOKEN: 'github_access_token',
  TOKEN_TYPE: 'github_token_type',
  SCOPE: 'github_scope',
  USER_INFO: 'github_user_info',
  AUTH_STATE: 'github_auth_state'
}

// GitHub用户权限范围说明
export const GITHUB_SCOPES = {
  'user': '读取用户基本信息',
  'user:email': '读取用户邮箱地址',
  'user:follow': '关注/取消关注用户',
  'public_repo': '访问公共仓库',
  'repo': '访问私有仓库',
  'repo_deployment': '访问部署状态',
  'repo:status': '访问提交状态',
  'delete_repo': '删除仓库',
  'notifications': '访问通知',
  'gist': '访问Gist',
  'read:repo_hook': '读取仓库钩子',
  'write:repo_hook': '写入仓库钩子',
  'admin:repo_hook': '管理仓库钩子',
  'admin:org_hook': '管理组织钩子',
  'read:org': '读取组织信息',
  'write:org': '写入组织信息',
  'admin:org': '管理组织',
  'read:public_key': '读取公钥',
  'write:public_key': '写入公钥',
  'admin:public_key': '管理公钥',
  'read:gpg_key': '读取GPG密钥',
  'write:gpg_key': '写入GPG密钥',
  'admin:gpg_key': '管理GPG密钥'
}

// GitHub API速率限制配置
export const GITHUB_RATE_LIMIT = {
  // 未认证请求：每小时60次
  unauthenticated: 60,
  // 认证请求：每小时5000次
  authenticated: 5000,
  // 搜索API：每分钟10次（未认证），每分钟30次（认证）
  search: {
    unauthenticated: 10,
    authenticated: 30
  }
}

// GitHub OAuth2应用类型
export const GITHUB_APP_TYPES = {
  OAUTH_APP: 'oauth_app',        // OAuth应用
  GITHUB_APP: 'github_app'       // GitHub应用
}
