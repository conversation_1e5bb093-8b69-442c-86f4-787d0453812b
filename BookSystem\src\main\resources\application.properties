spring.application.name=BookSystem
spring.jpa.database=mysql
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.jpa.hibernate.ddl-auto=create
spring.datasource.url=***************************************************
spring.datasource.username=root
spring.datasource.password=12345
server.port=8081
spring.data.jdbc.dialect=mysql

# JWT Configuration
jwt.expiration=86400000
jwt.refresh-expiration=604800000

# Date Format Configuration
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8
