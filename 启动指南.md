# 杭师大图书管理系统 - 启动指南

## 📁 项目结构

```
├── sa/                          # 前端项目 (Vue.js + Electron)
│   ├── src/                     # 源代码
│   ├── public/                  # 静态资源
│   └── package.json             # 前端依赖
├── BookSystem/                  # 后端项目 (Spring Boot)
│   ├── src/main/java/           # Java源代码
│   ├── src/main/resources/      # 配置文件
│   ├── node-analytics/          # Node.js数据分析服务
│   └── pom.xml                  # Maven配置
├── start-project.bat            # Windows启动脚本
├── start-project.ps1            # PowerShell启动脚本
└── 启动指南.md                  # 本文档
```

## 🔧 系统要求

### 必需软件
- **Java 21** - 后端Spring Boot应用
- **Node.js 16+** - 前端Vue.js和数据分析服务
- **Maven 3.6+** - Java项目构建工具
- **MySQL 8.0** - 主数据库 (可选，有H2内存数据库备选)
- **Redis** - 缓存和数据分析 (可选)

### 端口分配
- **8080** - 前端Vue.js开发服务器
- **8081** - 后端Spring Boot API服务器
- **3001** - Node.js数据分析服务
- **3306** - MySQL数据库
- **6379** - Redis缓存

## 🚀 快速启动

### 方法1: 使用启动脚本 (推荐)

**Windows:**
```bash
# 双击运行或在命令行执行
start-project.bat
```

**PowerShell:**
```powershell
# 右键"以PowerShell运行"或执行
.\start-project.ps1
```

### 方法2: 手动启动

#### 1. 启动后端服务
```bash
cd BookSystem
mvn spring-boot:run
```
- 服务地址: http://localhost:8081
- API文档: http://localhost:8081 (查看系统信息)

#### 2. 启动数据分析服务
```bash
cd BookSystem/node-analytics
npm install  # 首次运行
npm start
```
- 服务地址: http://localhost:3001

#### 3. 启动前端服务
```bash
cd sa
npm install  # 首次运行
npm run serve
```
- 应用地址: http://localhost:8080

## 👤 测试账户

| 用户名 | 密码 | 角色 | 权限 |
|--------|------|------|------|
| admin | admin123 | 管理员 | 完整系统管理权限 |
| user | user123 | 普通用户 | 图书借阅权限 |

## 🛠️ 开发模式

### 前端开发
```bash
cd sa
npm run serve          # 启动开发服务器
npm run build          # 构建生产版本
npm run electron-dev   # Electron开发模式
```

### 后端开发
```bash
cd BookSystem
mvn spring-boot:run    # 启动开发服务器
mvn clean package     # 构建JAR包
```

## 📊 功能特性

### 前端功能
- 📚 图书管理 (增删改查)
- 👥 用户管理
- 📈 数据统计图表
- 🔔 实时通知 (WebSocket)

- 🖥️ Electron桌面应用

### 后端功能
- 🔒 JWT身份认证
- 📡 RESTful API
- 🔄 WebSocket实时通信
- 📊 数据分析接口
- 🗄️ MySQL数据持久化
- ⚡ Redis缓存优化

## 🐛 常见问题

### 端口冲突
如果遇到端口占用，可以修改配置：
- 后端: `BookSystem/src/main/resources/application.properties`
- 前端: `sa/vue.config.js`
- 数据分析: `BookSystem/node-analytics/server.js`

### 数据库连接失败
1. 确保MySQL服务已启动
2. 检查数据库连接配置
3. 或使用H2内存数据库 (开发模式)

### 依赖安装失败
```bash
# 清理缓存重新安装
npm cache clean --force
npm install

# Maven依赖问题
mvn clean install -U
```

## 📞 技术支持

如遇问题，请检查：
1. 各服务的控制台输出
2. 浏览器开发者工具
3. 网络连接和防火墙设置
4. 软件版本兼容性

---
**开发团队**: 杭师大图书管理系统项目组
**最后更新**: 2024年12月
