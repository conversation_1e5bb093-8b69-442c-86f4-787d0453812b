<template>
  <div class="github-test-container">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <h2>GitHub OAuth2 认证测试页面</h2>
          <el-tag :type="authStatus.isAuthenticated ? 'success' : 'info'">
            {{ authStatus.isAuthenticated ? '已认证' : '未认证' }}
          </el-tag>
        </div>
      </template>

      <!-- 配置检查 -->
      <el-collapse v-model="activeCollapse">
        <el-collapse-item title="1. 配置检查" name="config">
          <div class="test-section">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="Client ID">
                <el-text :type="config.clientId !== 'your_github_client_id' ? 'success' : 'danger'">
                  {{ config.clientId }}
                </el-text>
              </el-descriptions-item>
              <el-descriptions-item label="回调地址">
                <el-text type="primary">{{ config.redirectUri }}</el-text>
              </el-descriptions-item>
              <el-descriptions-item label="授权范围">
                <el-tag size="small">{{ config.scope }}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="环境支持">
                <el-tag :type="support.isSupported ? 'success' : 'danger'" size="small">
                  {{ support.isSupported ? '支持' : '不支持' }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
            
            <div v-if="support.warnings.length > 0" class="warnings">
              <el-alert
                v-for="warning in support.warnings"
                :key="warning"
                :title="warning"
                type="warning"
                :closable="false"
                show-icon
              />
            </div>
          </div>
        </el-collapse-item>

        <el-collapse-item title="2. 认证状态" name="status">
          <div class="test-section">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="访问令牌">
                <el-text :type="authStatus.hasToken ? 'success' : 'info'">
                  {{ authStatus.hasToken ? '已获取' : '未获取' }}
                </el-text>
              </el-descriptions-item>
              <el-descriptions-item label="用户信息">
                <el-text :type="authStatus.hasUserInfo ? 'success' : 'info'">
                  {{ authStatus.hasUserInfo ? '已获取' : '未获取' }}
                </el-text>
              </el-descriptions-item>
              <el-descriptions-item label="令牌类型">
                <el-tag size="small">{{ authStatus.tokenType || '无' }}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="权限范围">
                <el-tag size="small">{{ authStatus.scope || '无' }}</el-tag>
              </el-descriptions-item>
            </el-descriptions>

            <div v-if="authStatus.userInfo" class="user-info">
              <h4>用户信息详情</h4>
              <el-descriptions :column="2" border>
                <el-descriptions-item label="用户名">
                  {{ authStatus.userInfo.login }}
                </el-descriptions-item>
                <el-descriptions-item label="显示名称">
                  {{ authStatus.userInfo.name || '未设置' }}
                </el-descriptions-item>
                <el-descriptions-item label="用户ID">
                  {{ authStatus.userInfo.id }}
                </el-descriptions-item>
                <el-descriptions-item label="头像">
                  <img :src="authStatus.userInfo.avatar_url" alt="avatar" class="avatar-small">
                </el-descriptions-item>
                <el-descriptions-item label="邮箱">
                  {{ authStatus.userInfo.email || '未公开' }}
                </el-descriptions-item>
                <el-descriptions-item label="公开仓库">
                  {{ authStatus.userInfo.public_repos }}
                </el-descriptions-item>
                <el-descriptions-item label="关注者">
                  {{ authStatus.userInfo.followers }}
                </el-descriptions-item>
                <el-descriptions-item label="关注中">
                  {{ authStatus.userInfo.following }}
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </el-collapse-item>

        <el-collapse-item title="3. 功能测试" name="functions">
          <div class="test-section">
            <div class="function-buttons">
              <el-button 
                type="primary" 
                @click="testGenerateAuthUrl"
                :loading="loading.generateUrl"
              >
                生成授权URL
              </el-button>
              
              <el-button 
                type="success" 
                @click="testRedirectToAuth"
                :loading="loading.redirect"
              >
                跳转到GitHub授权
              </el-button>
              
              <el-button 
                type="info" 
                @click="testValidateToken"
                :loading="loading.validate"
                :disabled="!authStatus.hasToken"
              >
                验证访问令牌
              </el-button>
              
              <el-button 
                type="warning" 
                @click="testGetUserInfo"
                :loading="loading.userInfo"
                :disabled="!authStatus.hasToken"
              >
                获取用户信息
              </el-button>
              
              <el-button 
                type="danger" 
                @click="testClearAuthData"
                :loading="loading.clear"
              >
                清除认证数据
              </el-button>
            </div>

            <div v-if="testResults.length > 0" class="test-results">
              <h4>测试结果</h4>
              <el-timeline>
                <el-timeline-item
                  v-for="(result, index) in testResults"
                  :key="index"
                  :timestamp="result.timestamp"
                  :type="result.success ? 'success' : 'danger'"
                >
                  <el-card>
                    <h5>{{ result.action }}</h5>
                    <p v-if="result.success" class="success-text">
                      ✅ {{ result.message }}
                    </p>
                    <p v-else class="error-text">
                      ❌ {{ result.error }}
                    </p>
                    <pre v-if="result.data" class="result-data">{{ JSON.stringify(result.data, null, 2) }}</pre>
                  </el-card>
                </el-timeline-item>
              </el-timeline>
            </div>
          </div>
        </el-collapse-item>

        <el-collapse-item title="4. API测试" name="api">
          <div class="test-section">
            <div class="api-test">
              <h4>模拟GitHub登录API调用</h4>
              <el-form :model="apiTestForm" label-width="120px">
                <el-form-item label="GitHub Token">
                  <el-input 
                    v-model="apiTestForm.githubToken" 
                    placeholder="输入GitHub访问令牌"
                    type="textarea"
                    :rows="3"
                  />
                </el-form-item>
                <el-form-item label="用户信息">
                  <el-input 
                    v-model="apiTestForm.userInfo" 
                    placeholder="输入用户信息JSON"
                    type="textarea"
                    :rows="5"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button 
                    type="primary" 
                    @click="testGithubLoginAPI"
                    :loading="loading.api"
                  >
                    测试GitHub登录API
                  </el-button>
                  <el-button @click="fillSampleData">
                    填充示例数据
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </el-collapse-item>

        <el-collapse-item title="5. 调试信息" name="debug">
          <div class="test-section">
            <div class="debug-info">
              <h4>本地存储数据</h4>
              <el-descriptions :column="1" border>
                <el-descriptions-item 
                  v-for="(value, key) in storageData" 
                  :key="key" 
                  :label="key"
                >
                  <pre class="storage-value">{{ value || '空' }}</pre>
                </el-descriptions-item>
              </el-descriptions>
              
              <div class="debug-actions">
                <el-button @click="refreshStorageData" size="small">
                  刷新存储数据
                </el-button>
                <el-button @click="exportDebugInfo" size="small" type="info">
                  导出调试信息
                </el-button>
              </div>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useStore } from 'vuex'
import { ElMessage, ElMessageBox } from 'element-plus'
import githubAuth from '@/services/githubAuth'
import { 
  checkGithubAuthStatus, 
  checkGithubSupport, 
  logGithubEvent,
  formatGithubUser,
  validateGithubUserInfo
} from '@/utils/githubUtils'
import { GITHUB_CONFIG, GITHUB_STORAGE_KEYS } from '@/config/githubConfig'
import api from '@/api'

export default {
  name: 'GithubAuthTest',
  setup() {
    const store = useStore()
    
    const activeCollapse = ref(['config', 'status'])
    const authStatus = ref({})
    const support = ref({})
    const config = ref(GITHUB_CONFIG)
    const testResults = ref([])
    const storageData = ref({})
    
    const loading = reactive({
      generateUrl: false,
      redirect: false,
      validate: false,
      userInfo: false,
      clear: false,
      api: false
    })

    const apiTestForm = reactive({
      githubToken: '',
      userInfo: ''
    })

    // 刷新认证状态
    const refreshAuthStatus = () => {
      authStatus.value = checkGithubAuthStatus()
      support.value = checkGithubSupport()
    }

    // 刷新存储数据
    const refreshStorageData = () => {
      storageData.value = {}
      Object.values(GITHUB_STORAGE_KEYS).forEach(key => {
        storageData.value[key] = localStorage.getItem(key)
      })
    }

    // 添加测试结果
    const addTestResult = (action, success, message, error = null, data = null) => {
      testResults.value.unshift({
        action,
        success,
        message,
        error,
        data,
        timestamp: new Date().toLocaleTimeString()
      })
      
      // 只保留最近10条结果
      if (testResults.value.length > 10) {
        testResults.value = testResults.value.slice(0, 10)
      }
    }

    // 测试生成授权URL
    const testGenerateAuthUrl = async () => {
      loading.generateUrl = true
      try {
        const authUrl = githubAuth.generateAuthUrl()
        addTestResult(
          '生成授权URL',
          true,
          '成功生成授权URL',
          null,
          { authUrl }
        )
        ElMessage.success('授权URL已生成，请查看测试结果')
      } catch (error) {
        addTestResult('生成授权URL', false, null, error.message)
        ElMessage.error('生成授权URL失败: ' + error.message)
      } finally {
        loading.generateUrl = false
      }
    }

    // 测试跳转到GitHub授权
    const testRedirectToAuth = async () => {
      loading.redirect = true
      try {
        await ElMessageBox.confirm(
          '即将跳转到GitHub授权页面，确认继续？',
          '确认跳转',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        githubAuth.redirectToAuth()
        addTestResult('跳转GitHub授权', true, '已跳转到GitHub授权页面')
      } catch (error) {
        if (error !== 'cancel') {
          addTestResult('跳转GitHub授权', false, null, error.message)
          ElMessage.error('跳转失败: ' + error.message)
        }
      } finally {
        loading.redirect = false
      }
    }

    // 测试验证访问令牌
    const testValidateToken = async () => {
      loading.validate = true
      try {
        const token = githubAuth.getStoredAccessToken()
        const result = await githubAuth.validateAccessToken(token)
        
        if (result.success) {
          addTestResult(
            '验证访问令牌',
            true,
            '访问令牌有效',
            null,
            result.data
          )
          ElMessage.success('访问令牌验证成功')
        } else {
          addTestResult('验证访问令牌', false, null, result.error)
          ElMessage.error('访问令牌验证失败: ' + result.error)
        }
      } catch (error) {
        addTestResult('验证访问令牌', false, null, error.message)
        ElMessage.error('验证失败: ' + error.message)
      } finally {
        loading.validate = false
        refreshAuthStatus()
      }
    }

    // 测试获取用户信息
    const testGetUserInfo = async () => {
      loading.userInfo = true
      try {
        const token = githubAuth.getStoredAccessToken()
        const result = await githubAuth.getUserInfo(token)
        
        if (result.success) {
          addTestResult(
            '获取用户信息',
            true,
            '用户信息获取成功',
            null,
            result.data
          )
          ElMessage.success('用户信息获取成功')
        } else {
          addTestResult('获取用户信息', false, null, result.error)
          ElMessage.error('获取用户信息失败: ' + result.error)
        }
      } catch (error) {
        addTestResult('获取用户信息', false, null, error.message)
        ElMessage.error('获取失败: ' + error.message)
      } finally {
        loading.userInfo = false
        refreshAuthStatus()
        refreshStorageData()
      }
    }

    // 测试清除认证数据
    const testClearAuthData = async () => {
      loading.clear = true
      try {
        await ElMessageBox.confirm(
          '确认清除所有GitHub认证数据？',
          '确认清除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        githubAuth.clearAuthData()
        addTestResult('清除认证数据', true, '认证数据已清除')
        ElMessage.success('认证数据已清除')
      } catch (error) {
        if (error !== 'cancel') {
          addTestResult('清除认证数据', false, null, error.message)
          ElMessage.error('清除失败: ' + error.message)
        }
      } finally {
        loading.clear = false
        refreshAuthStatus()
        refreshStorageData()
      }
    }

    // 填充示例数据
    const fillSampleData = () => {
      apiTestForm.githubToken = JSON.stringify({
        access_token: 'gho_example_token_1234567890',
        token_type: 'bearer',
        scope: 'user:email'
      }, null, 2)
      
      apiTestForm.userInfo = JSON.stringify({
        username: 'testuser',
        nickname: 'Test User',
        avatar: 'https://avatars.githubusercontent.com/u/1?v=4',
        email: '<EMAIL>',
        githubId: 12345,
        githubLogin: 'testuser',
        authType: 'github'
      }, null, 2)
    }

    // 测试GitHub登录API
    const testGithubLoginAPI = async () => {
      loading.api = true
      try {
        const githubToken = JSON.parse(apiTestForm.githubToken)
        const userInfo = JSON.parse(apiTestForm.userInfo)
        
        // 验证用户信息
        const validation = validateGithubUserInfo(userInfo)
        if (!validation.valid) {
          throw new Error('用户信息验证失败: ' + validation.errors.join(', '))
        }
        
        // 调用API
        const response = await api.auth.githubLogin({
          githubToken,
          userInfo
        })
        
        addTestResult(
          'GitHub登录API',
          response.success,
          response.success ? 'API调用成功' : null,
          response.success ? null : response.error,
          response.data
        )
        
        if (response.success) {
          ElMessage.success('GitHub登录API测试成功')
        } else {
          ElMessage.error('API调用失败: ' + response.error)
        }
      } catch (error) {
        addTestResult('GitHub登录API', false, null, error.message)
        ElMessage.error('API测试失败: ' + error.message)
      } finally {
        loading.api = false
      }
    }

    // 导出调试信息
    const exportDebugInfo = () => {
      const debugInfo = {
        timestamp: new Date().toISOString(),
        config: config.value,
        authStatus: authStatus.value,
        support: support.value,
        storageData: storageData.value,
        testResults: testResults.value
      }
      
      const blob = new Blob([JSON.stringify(debugInfo, null, 2)], {
        type: 'application/json'
      })
      
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `github-auth-debug-${Date.now()}.json`
      a.click()
      URL.revokeObjectURL(url)
      
      ElMessage.success('调试信息已导出')
    }

    // 组件挂载时初始化
    onMounted(() => {
      refreshAuthStatus()
      refreshStorageData()
      logGithubEvent('test_page_loaded')
    })

    return {
      activeCollapse,
      authStatus,
      support,
      config,
      testResults,
      storageData,
      loading,
      apiTestForm,
      refreshAuthStatus,
      refreshStorageData,
      testGenerateAuthUrl,
      testRedirectToAuth,
      testValidateToken,
      testGetUserInfo,
      testClearAuthData,
      fillSampleData,
      testGithubLoginAPI,
      exportDebugInfo
    }
  }
}
</script>
